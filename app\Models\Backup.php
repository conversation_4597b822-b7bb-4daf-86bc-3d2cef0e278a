<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Backup extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'filename',
        'path',
        'size',
        'type',
        'description',
        'status',
        'error_message',
        'backup_date'
    ];

    protected $casts = [
        'backup_date' => 'datetime',
        'size' => 'integer'
    ];

    /**
     * Get formatted file size
     */
    public function getFormattedSizeAttribute()
    {
        $bytes = $this->size;
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];

        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }

        return round($bytes, 2) . ' ' . $units[$i];
    }

    /**
     * Check if backup file exists
     */
    public function fileExists()
    {
        return file_exists($this->path);
    }

    /**
     * Get status badge class for UI
     */
    public function getStatusBadgeClass()
    {
        return match($this->status) {
            'completed' => 'badge-success',
            'failed' => 'badge-danger',
            'pending' => 'badge-warning',
            default => 'badge-secondary'
        };
    }

    /**
     * Get status text in Arabic
     */
    public function getStatusTextAttribute()
    {
        return match($this->status) {
            'completed' => 'مكتملة',
            'failed' => 'فاشلة',
            'pending' => 'قيد المعالجة',
            default => 'غير معروف'
        };
    }
}
