<?php $__env->startSection('title', 'تفاصيل النسخة الاحتياطية - ' . $backup->name); ?>

<?php $__env->startSection('content'); ?>
<div class="row">
    <div class="col-md-8">
        <!-- معلومات النسخة الاحتياطية -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-info-circle text-primary me-2"></i>
                    تفاصيل النسخة الاحتياطية
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6><i class="fas fa-tag text-primary me-1"></i> الاسم</h6>
                        <p class="mb-3"><?php echo e($backup->name); ?></p>

                        <h6><i class="fas fa-file text-info me-1"></i> اسم الملف</h6>
                        <p class="mb-3"><code><?php echo e($backup->filename); ?></code></p>

                        <h6><i class="fas fa-calendar text-success me-1"></i> تاريخ الإنشاء</h6>
                        <p class="mb-3">
                            <?php echo e($backup->backup_date->format('Y-m-d H:i:s')); ?>

                            <br>
                            <small class="text-muted"><?php echo e($backup->backup_date->diffForHumans()); ?></small>
                        </p>

                        <h6><i class="fas fa-database text-warning me-1"></i> نوع قاعدة البيانات</h6>
                        <p class="mb-3">
                            <span class="badge bg-info"><?php echo e(strtoupper($backup->type)); ?></span>
                        </p>
                    </div>
                    <div class="col-md-6">
                        <h6><i class="fas fa-check-circle me-1"></i> الحالة</h6>
                        <p class="mb-3">
                            <span class="badge <?php echo e($backup->getStatusBadgeClass()); ?>">
                                <?php echo e($backup->status_text); ?>

                            </span>
                        </p>

                        <?php if($backup->status === 'completed'): ?>
                            <h6><i class="fas fa-weight text-secondary me-1"></i> حجم الملف</h6>
                            <p class="mb-3"><?php echo e($backup->formatted_size); ?></p>

                            <h6><i class="fas fa-folder text-warning me-1"></i> مسار الملف</h6>
                            <p class="mb-3">
                                <code class="small"><?php echo e($backup->path); ?></code>
                            </p>

                            <h6><i class="fas fa-check text-success me-1"></i> حالة الملف</h6>
                            <p class="mb-3">
                                <?php if($backup->fileExists()): ?>
                                    <span class="badge bg-success">
                                        <i class="fas fa-check me-1"></i>
                                        الملف موجود
                                    </span>
                                <?php else: ?>
                                    <span class="badge bg-danger">
                                        <i class="fas fa-times me-1"></i>
                                        الملف غير موجود
                                    </span>
                                <?php endif; ?>
                            </p>
                        <?php endif; ?>

                        <?php if($backup->status === 'failed' && $backup->error_message): ?>
                            <h6><i class="fas fa-exclamation-triangle text-danger me-1"></i> رسالة الخطأ</h6>
                            <div class="alert alert-danger">
                                <pre class="mb-0 small"><?php echo e($backup->error_message); ?></pre>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>

                <?php if($backup->description): ?>
                    <hr>
                    <h6><i class="fas fa-comment text-info me-1"></i> الوصف</h6>
                    <p class="mb-0"><?php echo e($backup->description); ?></p>
                <?php endif; ?>
            </div>
        </div>

        <!-- معلومات إضافية -->
        <div class="card mt-4">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-chart-bar text-primary me-1"></i>
                    معلومات إضافية
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <p class="mb-2">
                            <strong>تاريخ آخر تحديث:</strong>
                            <br>
                            <?php echo e($backup->updated_at->format('Y-m-d H:i:s')); ?>

                        </p>
                    </div>
                    <div class="col-md-6">
                        <p class="mb-2">
                            <strong>المعرف الفريد:</strong>
                            <br>
                            <code>#<?php echo e($backup->id); ?></code>
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- الإجراءات -->
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-cogs text-primary me-1"></i>
                    الإجراءات المتاحة
                </h6>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <?php if($backup->status === 'completed' && $backup->fileExists()): ?>
                        <a href="<?php echo e(route('backups.download', $backup)); ?>" 
                           class="btn btn-success">
                            <i class="fas fa-download me-1"></i>
                            تنزيل النسخة الاحتياطية
                        </a>

                        <form action="<?php echo e(route('backups.restore', $backup)); ?>" 
                              method="POST" 
                              onsubmit="return confirmRestore()">
                            <?php echo csrf_field(); ?>
                            <button type="submit" class="btn btn-warning w-100">
                                <i class="fas fa-undo me-1"></i>
                                استعادة النسخة الاحتياطية
                            </button>
                        </form>

                        <hr>
                    <?php endif; ?>

                    <form action="<?php echo e(route('backups.destroy', $backup)); ?>" 
                          method="POST" 
                          onsubmit="return confirmDelete()">
                        <?php echo csrf_field(); ?>
                        <?php echo method_field('DELETE'); ?>
                        <button type="submit" class="btn btn-danger w-100">
                            <i class="fas fa-trash me-1"></i>
                            حذف النسخة الاحتياطية
                        </button>
                    </form>

                    <a href="<?php echo e(route('backups.index')); ?>" class="btn btn-secondary">
                        <i class="fas fa-arrow-right me-1"></i>
                        العودة للقائمة
                    </a>
                </div>
            </div>
        </div>

        <!-- تحذيرات -->
        <?php if($backup->status === 'completed'): ?>
            <div class="card mt-3">
                <div class="card-header bg-warning text-dark">
                    <h6 class="mb-0">
                        <i class="fas fa-exclamation-triangle me-1"></i>
                        تحذيرات مهمة
                    </h6>
                </div>
                <div class="card-body">
                    <div class="alert alert-warning mb-2">
                        <small>
                            <strong>استعادة النسخة الاحتياطية:</strong>
                            سيتم استبدال جميع البيانات الحالية في قاعدة البيانات بالبيانات الموجودة في هذه النسخة.
                        </small>
                    </div>
                    <div class="alert alert-danger mb-0">
                        <small>
                            <strong>حذف النسخة:</strong>
                            لن يمكن استرداد النسخة الاحتياطية بعد حذفها.
                        </small>
                    </div>
                </div>
            </div>
        <?php endif; ?>

        <?php if($backup->status === 'failed'): ?>
            <div class="card mt-3">
                <div class="card-header bg-danger text-white">
                    <h6 class="mb-0">
                        <i class="fas fa-times-circle me-1"></i>
                        فشل في الإنشاء
                    </h6>
                </div>
                <div class="card-body">
                    <p class="small mb-2">
                        فشل في إنشاء هذه النسخة الاحتياطية. يمكنك المحاولة مرة أخرى بإنشاء نسخة جديدة.
                    </p>
                    <a href="<?php echo e(route('backups.create')); ?>" class="btn btn-primary btn-sm">
                        <i class="fas fa-plus me-1"></i>
                        إنشاء نسخة جديدة
                    </a>
                </div>
            </div>
        <?php endif; ?>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
function confirmRestore() {
    return confirm(
        'هل أنت متأكد من استعادة هذه النسخة الاحتياطية؟\n\n' +
        'تحذير: سيتم استبدال جميع البيانات الحالية في قاعدة البيانات!\n' +
        'تأكد من إنشاء نسخة احتياطية من البيانات الحالية قبل المتابعة.\n\n' +
        'اضغط موافق للمتابعة أو إلغاء للتراجع.'
    );
}

function confirmDelete() {
    return confirm(
        'هل أنت متأكد من حذف هذه النسخة الاحتياطية؟\n\n' +
        'تحذير: لن يمكن استرداد النسخة بعد حذفها!\n\n' +
        'اضغط موافق للحذف أو إلغاء للتراجع.'
    );
}
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\laragon\www\test2\resources\views/backups/show.blade.php ENDPATH**/ ?>