<?php

namespace App\Services;

use App\Models\Backup;
use Exception;

class BackupService
{
    private $backupPath;
    
    public function __construct()
    {
        $this->backupPath = storage_path('app/backups');
        
        // إنشاء مجلد النسخ الاحتياطية إذا لم يكن موجوداً
        if (!file_exists($this->backupPath)) {
            mkdir($this->backupPath, 0755, true);
        }
    }

    /**
     * إنشاء نسخة احتياطية من قاعدة البيانات
     */
    public function createBackup($name = null, $description = null)
    {
        try {
            $name = $name ?: 'backup_' . date('Y-m-d_H-i-s');
            $filename = $name . '.sql';
            $filepath = $this->backupPath . '/' . $filename;

            // إنشاء سجل في قاعدة البيانات
            $backup = Backup::create([
                'name' => $name,
                'filename' => $filename,
                'path' => $filepath,
                'size' => 0,
                'type' => 'mysql',
                'description' => $description,
                'status' => 'pending'
            ]);

            // الحصول على إعدادات قاعدة البيانات
            $host = config('database.connections.mysql.host');
            $port = config('database.connections.mysql.port');
            $database = config('database.connections.mysql.database');
            $username = config('database.connections.mysql.username');
            $password = config('database.connections.mysql.password');

            // بناء أمر mysqldump مع التحقق من نظام التشغيل
            $isWindows = strtoupper(substr(PHP_OS, 0, 3)) === 'WIN';

            if ($isWindows) {
                // في Windows، نحتاج لاستخدام مسار كامل لـ mysqldump
                $mysqldumpPath = $this->findMysqldumpPath();

                if ($password) {
                    $command = sprintf(
                        '"%s" --host=%s --port=%s --user=%s --password=%s --single-transaction --routines --triggers --lock-tables=false %s > "%s"',
                        $mysqldumpPath,
                        $host,
                        $port,
                        $username,
                        $password,
                        $database,
                        $filepath
                    );
                } else {
                    $command = sprintf(
                        '"%s" --host=%s --port=%s --user=%s --single-transaction --routines --triggers --lock-tables=false %s > "%s"',
                        $mysqldumpPath,
                        $host,
                        $port,
                        $username,
                        $database,
                        $filepath
                    );
                }
            } else {
                // في Linux/Mac
                $command = sprintf(
                    'mysqldump --host=%s --port=%s --user=%s --password=%s --single-transaction --routines --triggers %s > %s',
                    escapeshellarg($host),
                    escapeshellarg($port),
                    escapeshellarg($username),
                    escapeshellarg($password),
                    escapeshellarg($database),
                    escapeshellarg($filepath)
                );
            }

            // تنفيذ الأمر
            $output = [];
            $returnCode = 0;
            exec($command . ' 2>&1', $output, $returnCode);

            if ($returnCode === 0 && file_exists($filepath)) {
                // تحديث معلومات النسخة الاحتياطية
                $backup->update([
                    'size' => filesize($filepath),
                    'status' => 'completed'
                ]);

                return [
                    'success' => true,
                    'message' => 'تم إنشاء النسخة الاحتياطية بنجاح',
                    'backup' => $backup
                ];
            } else {
                // في حالة الفشل
                $backup->update([
                    'status' => 'failed',
                    'error_message' => implode("\n", $output)
                ]);

                return [
                    'success' => false,
                    'message' => 'فشل في إنشاء النسخة الاحتياطية',
                    'error' => implode("\n", $output)
                ];
            }

        } catch (Exception $e) {
            if (isset($backup)) {
                $backup->update([
                    'status' => 'failed',
                    'error_message' => $e->getMessage()
                ]);
            }

            return [
                'success' => false,
                'message' => 'حدث خطأ: ' . $e->getMessage()
            ];
        }
    }

    /**
     * استعادة نسخة احتياطية
     */
    public function restoreBackup($backupId)
    {
        try {
            $backup = Backup::findOrFail($backupId);

            if (!$backup->fileExists()) {
                return [
                    'success' => false,
                    'message' => 'ملف النسخة الاحتياطية غير موجود'
                ];
            }

            // الحصول على إعدادات قاعدة البيانات
            $host = config('database.connections.mysql.host');
            $port = config('database.connections.mysql.port');
            $database = config('database.connections.mysql.database');
            $username = config('database.connections.mysql.username');
            $password = config('database.connections.mysql.password');

            // بناء أمر mysql للاستعادة مع التحقق من نظام التشغيل
            $isWindows = strtoupper(substr(PHP_OS, 0, 3)) === 'WIN';

            if ($isWindows) {
                // في Windows، نحتاج لاستخدام مسار كامل لـ mysql
                $mysqlPath = $this->findMysqlPath();

                if ($password) {
                    $command = sprintf(
                        '"%s" --host=%s --port=%s --user=%s --password=%s --force %s < "%s"',
                        $mysqlPath,
                        $host,
                        $port,
                        $username,
                        $password,
                        $database,
                        $backup->path
                    );
                } else {
                    $command = sprintf(
                        '"%s" --host=%s --port=%s --user=%s --force %s < "%s"',
                        $mysqlPath,
                        $host,
                        $port,
                        $username,
                        $database,
                        $backup->path
                    );
                }
            } else {
                // في Linux/Mac
                $command = sprintf(
                    'mysql --host=%s --port=%s --user=%s --password=%s %s < %s',
                    escapeshellarg($host),
                    escapeshellarg($port),
                    escapeshellarg($username),
                    escapeshellarg($password),
                    escapeshellarg($database),
                    escapeshellarg($backup->path)
                );
            }

            // تنفيذ الأمر
            $output = [];
            $returnCode = 0;
            exec($command . ' 2>&1', $output, $returnCode);

            if ($returnCode === 0) {
                return [
                    'success' => true,
                    'message' => 'تم استعادة النسخة الاحتياطية بنجاح'
                ];
            } else {
                return [
                    'success' => false,
                    'message' => 'فشل في استعادة النسخة الاحتياطية',
                    'error' => implode("\n", $output)
                ];
            }

        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => 'حدث خطأ: ' . $e->getMessage()
            ];
        }
    }

    /**
     * حذف نسخة احتياطية
     */
    public function deleteBackup($backupId)
    {
        try {
            $backup = Backup::findOrFail($backupId);

            // حذف الملف إذا كان موجوداً
            if ($backup->fileExists()) {
                unlink($backup->path);
            }

            // حذف السجل من قاعدة البيانات
            $backup->delete();

            return [
                'success' => true,
                'message' => 'تم حذف النسخة الاحتياطية بنجاح'
            ];

        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => 'حدث خطأ: ' . $e->getMessage()
            ];
        }
    }

    /**
     * تنزيل نسخة احتياطية
     */
    public function downloadBackup($backupId)
    {
        try {
            $backup = Backup::findOrFail($backupId);

            if (!$backup->fileExists()) {
                return [
                    'success' => false,
                    'message' => 'ملف النسخة الاحتياطية غير موجود'
                ];
            }

            return [
                'success' => true,
                'path' => $backup->path,
                'filename' => $backup->filename
            ];

        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => 'حدث خطأ: ' . $e->getMessage()
            ];
        }
    }

    /**
     * الحصول على جميع النسخ الاحتياطية
     */
    public function getAllBackups()
    {
        return Backup::orderBy('created_at', 'desc')->get();
    }

    /**
     * تنظيف النسخ الاحتياطية القديمة
     */
    public function cleanOldBackups($daysToKeep = 30)
    {
        $cutoffDate = now()->subDays($daysToKeep);

        $oldBackups = Backup::where('created_at', '<', $cutoffDate)->get();

        $deletedCount = 0;
        foreach ($oldBackups as $backup) {
            $result = $this->deleteBackup($backup->id);
            if ($result['success']) {
                $deletedCount++;
            }
        }

        return [
            'success' => true,
            'message' => "تم حذف {$deletedCount} نسخة احتياطية قديمة"
        ];
    }

    /**
     * البحث عن مسار mysqldump في Windows
     */
    private function findMysqldumpPath()
    {
        // مسارات محتملة لـ mysqldump في Windows
        $possiblePaths = [
            'mysqldump', // إذا كان في PATH
            'C:\laragon\bin\mysql\mysql-8.0.30\bin\mysqldump.exe',
            'C:\laragon\bin\mysql\mysql-5.7.33\bin\mysqldump.exe',
            'C:\xampp\mysql\bin\mysqldump.exe',
            'C:\wamp64\bin\mysql\mysql8.0.21\bin\mysqldump.exe',
            'C:\Program Files\MySQL\MySQL Server 8.0\bin\mysqldump.exe',
            'C:\Program Files\MySQL\MySQL Server 5.7\bin\mysqldump.exe',
        ];

        foreach ($possiblePaths as $path) {
            // اختبار إذا كان المسار يعمل
            $testCommand = '"' . $path . '" --version 2>nul';
            $output = [];
            $returnCode = 0;
            exec($testCommand, $output, $returnCode);

            if ($returnCode === 0) {
                return $path;
            }
        }

        // إذا لم نجد mysqldump، نحاول البحث في مجلدات Laragon
        $laragonPath = 'C:\laragon\bin\mysql';
        if (is_dir($laragonPath)) {
            $mysqlDirs = glob($laragonPath . '\mysql-*');
            foreach ($mysqlDirs as $dir) {
                $mysqldumpPath = $dir . '\bin\mysqldump.exe';
                if (file_exists($mysqldumpPath)) {
                    return $mysqldumpPath;
                }
            }
        }

        // كحل أخير، نعيد mysqldump ونأمل أن يكون في PATH
        return 'mysqldump';
    }

    /**
     * البحث عن مسار mysql في Windows
     */
    private function findMysqlPath()
    {
        // مسارات محتملة لـ mysql في Windows
        $possiblePaths = [
            'mysql', // إذا كان في PATH
            'C:\laragon\bin\mysql\mysql-8.0.30\bin\mysql.exe',
            'C:\laragon\bin\mysql\mysql-5.7.33\bin\mysql.exe',
            'C:\xampp\mysql\bin\mysql.exe',
            'C:\wamp64\bin\mysql\mysql8.0.21\bin\mysql.exe',
            'C:\Program Files\MySQL\MySQL Server 8.0\bin\mysql.exe',
            'C:\Program Files\MySQL\MySQL Server 5.7\bin\mysql.exe',
        ];

        foreach ($possiblePaths as $path) {
            // اختبار إذا كان المسار يعمل
            $testCommand = '"' . $path . '" --version 2>nul';
            $output = [];
            $returnCode = 0;
            exec($testCommand, $output, $returnCode);

            if ($returnCode === 0) {
                return $path;
            }
        }

        // إذا لم نجد mysql، نحاول البحث في مجلدات Laragon
        $laragonPath = 'C:\laragon\bin\mysql';
        if (is_dir($laragonPath)) {
            $mysqlDirs = glob($laragonPath . '\mysql-*');
            foreach ($mysqlDirs as $dir) {
                $mysqlPath = $dir . '\bin\mysql.exe';
                if (file_exists($mysqlPath)) {
                    return $mysqlPath;
                }
            }
        }

        // كحل أخير، نعيد mysql ونأمل أن يكون في PATH
        return 'mysql';
    }
}
