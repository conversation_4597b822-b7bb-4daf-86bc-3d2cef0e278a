<?php

/**
 * سكريبت اختبار نظام النسخ الاحتياطية
 * يقوم بإنشاء بيانات تجريبية واختبار جميع وظائف النظام
 */

require_once 'vendor/autoload.php';

// تحميل متغيرات البيئة
$dotenv = Dotenv\Dotenv::createImmutable(__DIR__);
$dotenv->load();

// إعدادات قاعدة البيانات
$host = $_ENV['DB_HOST'] ?? '127.0.0.1';
$port = $_ENV['DB_PORT'] ?? '3306';
$database = $_ENV['DB_DATABASE'] ?? 'test2';
$username = $_ENV['DB_USERNAME'] ?? 'root';
$password = $_ENV['DB_PASSWORD'] ?? '';

echo "🧪 بدء اختبار نظام النسخ الاحتياطية...\n";
echo "📊 قاعدة البيانات: {$database}\n\n";

try {
    // الاتصال بقاعدة البيانات
    $pdo = new PDO("mysql:host={$host};port={$port};dbname={$database}", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "✅ تم الاتصال بقاعدة البيانات بنجاح\n";
    
    // إنشاء بيانات تجريبية
    echo "\n📝 إنشاء بيانات تجريبية...\n";
    
    // إنشاء جدول تجريبي
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS `test_data` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `name` varchar(100) NOT NULL,
            `email` varchar(100) NOT NULL,
            `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
    ");
    
    // إدراج بيانات تجريبية
    $testData = [
        ['أحمد محمد', '<EMAIL>'],
        ['فاطمة علي', '<EMAIL>'],
        ['محمد حسن', '<EMAIL>'],
        ['عائشة أحمد', '<EMAIL>'],
        ['علي محمود', '<EMAIL>'],
        ['زينب سالم', '<EMAIL>'],
        ['يوسف عبدالله', '<EMAIL>'],
        ['مريم خالد', '<EMAIL>'],
        ['عمر سعيد', '<EMAIL>'],
        ['نور الدين', '<EMAIL>']
    ];
    
    $stmt = $pdo->prepare("INSERT INTO test_data (name, email) VALUES (?, ?)");
    
    foreach ($testData as $data) {
        $stmt->execute($data);
    }
    
    echo "✅ تم إدراج " . count($testData) . " سجل تجريبي\n";
    
    // إنشاء بعض المستخدمين التجريبيين
    $users = [
        ['المدير العام', '<EMAIL>', password_hash('admin123', PASSWORD_DEFAULT)],
        ['مدير النظام', '<EMAIL>', password_hash('system123', PASSWORD_DEFAULT)],
        ['المطور', '<EMAIL>', password_hash('dev123', PASSWORD_DEFAULT)]
    ];
    
    $stmt = $pdo->prepare("INSERT IGNORE INTO users (name, email, password, created_at, updated_at) VALUES (?, ?, ?, NOW(), NOW())");
    
    foreach ($users as $user) {
        $stmt->execute($user);
    }
    
    echo "✅ تم إدراج " . count($users) . " مستخدم تجريبي\n";
    
    // إحصائيات قاعدة البيانات
    echo "\n📊 إحصائيات قاعدة البيانات:\n";
    
    // عدد الجداول
    $result = $pdo->query("SELECT COUNT(*) as table_count FROM information_schema.tables WHERE table_schema = '{$database}'");
    $tableCount = $result->fetch()['table_count'];
    echo "📋 عدد الجداول: {$tableCount}\n";
    
    // عدد المستخدمين
    $result = $pdo->query("SELECT COUNT(*) as user_count FROM users");
    $userCount = $result->fetch()['user_count'];
    echo "👥 عدد المستخدمين: {$userCount}\n";
    
    // عدد البيانات التجريبية
    $result = $pdo->query("SELECT COUNT(*) as test_count FROM test_data");
    $testCount = $result->fetch()['test_count'];
    echo "🧪 عدد البيانات التجريبية: {$testCount}\n";
    
    // حجم قاعدة البيانات
    $result = $pdo->query("
        SELECT 
            ROUND(SUM(data_length + index_length) / 1024 / 1024, 2) AS size_mb
        FROM information_schema.tables 
        WHERE table_schema = '{$database}'
    ");
    $sizeResult = $result->fetch();
    $sizeMB = $sizeResult['size_mb'] ?? 0;
    echo "💾 حجم قاعدة البيانات: {$sizeMB} ميجابايت\n";
    
    // اختبار إنشاء نسخة احتياطية تجريبية
    echo "\n🔧 اختبار إنشاء نسخة احتياطية...\n";

    $backupPath = __DIR__ . '/storage/app/backups';
    if (!file_exists($backupPath)) {
        mkdir($backupPath, 0755, true);
        echo "✅ تم إنشاء مجلد النسخ الاحتياطية\n";
    }

    $testBackupFile = $backupPath . '/test_backup_' . date('Y-m-d_H-i-s') . '.sql';

    // البحث عن mysqldump في Windows
    function findMysqldump() {
        $possiblePaths = [
            'mysqldump',
            'C:\laragon\bin\mysql\mysql-8.0.30\bin\mysqldump.exe',
            'C:\laragon\bin\mysql\mysql-5.7.33\bin\mysqldump.exe',
            'C:\xampp\mysql\bin\mysqldump.exe',
        ];

        foreach ($possiblePaths as $path) {
            $testCommand = '"' . $path . '" --version 2>nul';
            $output = [];
            $returnCode = 0;
            exec($testCommand, $output, $returnCode);

            if ($returnCode === 0) {
                return $path;
            }
        }

        // البحث في مجلدات Laragon
        $laragonPath = 'C:\laragon\bin\mysql';
        if (is_dir($laragonPath)) {
            $mysqlDirs = glob($laragonPath . '\mysql-*');
            foreach ($mysqlDirs as $dir) {
                $mysqldumpPath = $dir . '\bin\mysqldump.exe';
                if (file_exists($mysqldumpPath)) {
                    return $mysqldumpPath;
                }
            }
        }

        return 'mysqldump';
    }

    $mysqldumpPath = findMysqldump();
    echo "🔍 مسار mysqldump: {$mysqldumpPath}\n";

    // بناء أمر mysqldump للاختبار
    if ($password) {
        $command = sprintf(
            '"%s" --host=%s --port=%s --user=%s --password=%s --single-transaction --routines --triggers --lock-tables=false %s > "%s"',
            $mysqldumpPath,
            $host,
            $port,
            $username,
            $password,
            $database,
            $testBackupFile
        );
    } else {
        $command = sprintf(
            '"%s" --host=%s --port=%s --user=%s --single-transaction --routines --triggers --lock-tables=false %s > "%s"',
            $mysqldumpPath,
            $host,
            $port,
            $username,
            $database,
            $testBackupFile
        );
    }

    echo "🔧 تنفيذ الأمر: " . substr($command, 0, 100) . "...\n";

    // محاولة تنفيذ الأمر
    $output = [];
    $returnCode = 0;
    exec($command . ' 2>&1', $output, $returnCode);

    if ($returnCode === 0 && file_exists($testBackupFile)) {
        $backupSize = filesize($testBackupFile);
        $backupSizeKB = round($backupSize / 1024, 2);
        echo "✅ تم إنشاء نسخة احتياطية تجريبية بنجاح\n";
        echo "📁 مسار الملف: {$testBackupFile}\n";
        echo "📏 حجم الملف: {$backupSizeKB} كيلوبايت\n";

        // حذف الملف التجريبي
        unlink($testBackupFile);
        echo "🗑️ تم حذف الملف التجريبي\n";
    } else {
        echo "❌ فشل في إنشاء النسخة الاحتياطية التجريبية\n";
        echo "🔍 كود الخطأ: {$returnCode}\n";
        echo "🔍 رسالة الخطأ: " . implode("\n", $output) . "\n";
        echo "💡 الحلول المحتملة:\n";
        echo "   - تأكد من تشغيل Laragon أو XAMPP\n";
        echo "   - تحقق من مسار mysqldump\n";
        echo "   - تأكد من صلاحيات المستخدم\n";
    }
    
    // اختبار الوصول للواجهة
    echo "\n🌐 اختبار الوصول للواجهة...\n";
    echo "🔗 رابط النظام: http://localhost:8000/backups\n";
    echo "📱 رابط إنشاء نسخة: http://localhost:8000/backups/create\n";
    
    // نصائح للاستخدام
    echo "\n💡 نصائح للاستخدام:\n";
    echo "1. تأكد من تشغيل خادم Laravel: php artisan serve\n";
    echo "2. افتح المتصفح وانتقل للرابط أعلاه\n";
    echo "3. جرب إنشاء نسخة احتياطية جديدة\n";
    echo "4. اختبر تنزيل واستعادة النسخ\n";
    echo "5. راجع ملف README_BACKUP_SYSTEM.md للتفاصيل\n";
    
    echo "\n🎉 تم اختبار النظام بنجاح!\n";
    echo "✨ النظام جاهز للاستخدام\n";
    
} catch (PDOException $e) {
    echo "❌ خطأ في قاعدة البيانات: " . $e->getMessage() . "\n";
    echo "💡 تأكد من:\n";
    echo "   - تشغيل خادم MySQL\n";
    echo "   - صحة إعدادات .env\n";
    echo "   - وجود قاعدة البيانات\n";
    exit(1);
} catch (Exception $e) {
    echo "❌ خطأ عام: " . $e->getMessage() . "\n";
    exit(1);
}

echo "\n" . str_repeat("=", 50) . "\n";
echo "🚀 للبدء في استخدام النظام:\n";
echo "   php artisan serve\n";
echo "   ثم افتح: http://localhost:8000/backups\n";
echo str_repeat("=", 50) . "\n";
